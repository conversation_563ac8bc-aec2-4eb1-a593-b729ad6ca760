const API_ENDPOINT = "https://c.unity.cn";

const APP_ID = "1156179e-6f72-4b30-940a-8f7fca6efaa5";
const APP_SERVICE_SECRET = "6c72cddc6a644625b7bc4e8980b48617";

function get_basic_authorization(app_id, app_secret) {
    /** 获取basic auth的Header */
    const credentials = `${app_id}:${app_secret}`;
    const encodedCredentials = btoa(credentials);
    return { 'Authorization': `Basic ${encodedCredentials}` };
}

exports.SendRequest = async function (method, url, data = null) {
    /** 发送请求 */
    const options = {
        method: method,
        url: `${API_ENDPOINT}/${url}`,
        headers: {
            'Content-Type': 'application/json',
            ...get_basic_authorization(APP_ID, APP_SERVICE_SECRET),
        },
        responseType: 'json',
    };

    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open(options.method, options.url, true);
        xhr.timeout = 5000;

        for (const header in options.headers) {
            xhr.setRequestHeader(header, options.headers[header]);
        }

        xhr.onload = function () {
            if (xhr.status >= 200 && xhr.status < 300) {
                resolve(JSON.parse(xhr.responseText));
            } else {
                reject(new Error(`Request failed with status ${xhr.status}`));
            }
        };

        xhr.ontimeout = function () {
            reject(new Error('Timeout error'));
        };

        xhr.onerror = function () {
            reject(new Error('Network error'));
        };

        if (data) {
            xhr.send(JSON.stringify(data));
        } else {
            xhr.send();
        }
    });
}

exports.SendNormalRequest = async function (method, url, data = null) {
    /** 发送请求 */
    const options = {
        method: method,
        url: url,
        responseType: 'json',
        headers: {
            'Content-Type': 'application/json',
        },
    };

    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open(options.method, options.url, true);
        xhr.timeout = 5000;
        for (const header in options.headers) {
            xhr.setRequestHeader(header, options.headers[header]);
        }

        xhr.onload = function () {
            if (xhr.status >= 200 && xhr.status < 300) {
                resolve(xhr.responseText);
            } else {
                reject(new Error(`Request failed with status ${xhr.status}`));
            }
        };

        xhr.ontimeout = function () {
            reject(new Error('Timeout error'));
        };

        xhr.onerror = function () {
            reject(new Error('Network error'));
        };

        if (data) {
            xhr.send(JSON.stringify(data));
        } else {
            xhr.send();
        }
    });
}
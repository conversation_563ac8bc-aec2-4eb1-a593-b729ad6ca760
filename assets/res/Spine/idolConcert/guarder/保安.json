{"skeleton": {"hash": "SRLOYQWHCqHbKZOg7Aqm0kyNB7o", "spine": "3.8.75", "x": 10.04, "y": -437, "width": 145.96, "height": 416, "images": "./images/", "audio": "//FTP-Yike/公用/游戏Assert/2023-07-01_就我眼神好/关卡资源（动效）/爱豆演唱会/源文件/保安"}, "bones": [{"name": "root"}, {"name": "dingdian", "parent": "root", "x": 99.73, "y": -193.98}, {"name": "shenti", "parent": "dingdian", "length": 204.19, "rotation": 92.32, "x": 1.84, "y": -40.44}, {"name": "shang<PERSON>", "parent": "shenti", "length": 59.27, "rotation": 170.55, "x": 123.14, "y": 42.83}, {"name": "<PERSON><PERSON><PERSON>", "parent": "shang<PERSON>", "length": 63.44, "rotation": 8.79, "x": 65.09, "y": -2.05}, {"name": "datui1", "parent": "shenti", "length": 186.46, "rotation": 172.31, "x": 5.52, "y": 22.77}, {"name": "datui", "parent": "shenti", "length": 143.2, "rotation": -176.43, "x": -11.32, "y": -6.9}, {"name": "shoubi", "parent": "root", "x": 71.21, "y": -222.92, "color": "ff3f00ff"}], "slots": [{"name": "身体", "bone": "shenti", "attachment": "身体"}, {"name": "右腿", "bone": "datui", "attachment": "右腿"}, {"name": "左腿", "bone": "datui1", "attachment": "左腿"}, {"name": "上臂", "bone": "shang<PERSON>", "attachment": "上臂"}, {"name": "小臂", "bone": "<PERSON><PERSON><PERSON>", "attachment": "小臂"}], "ik": [{"name": "shoubi", "target": "shoubi", "bones": ["shang<PERSON>", "<PERSON><PERSON><PERSON>"]}], "skins": [{"name": "default", "attachments": {"身体": {"身体": {"x": 88.43, "y": -1.52, "rotation": -92.32, "width": 102, "height": 250}}, "小臂": {"小臂": {"x": 29.32, "y": -2.3, "rotation": 88.34, "width": 30, "height": 84}}, "左腿": {"左腿": {"x": 88.86, "y": 2.24, "rotation": 95.37, "width": 65, "height": 233}}, "右腿": {"右腿": {"x": 62.68, "y": 7.69, "rotation": 84.11, "width": 66, "height": 175}}, "上臂": {"上臂": {"x": 30.93, "y": -1.84, "rotation": 97.13, "width": 38, "height": 91}}}}], "animations": {"xiaban": {"bones": {"shoubi": {"translate": [{"x": -3.58, "y": 1.29}, {"time": 0.1667, "x": -7.98, "y": 7.61}, {"time": 0.3333, "x": -3.58, "y": 1.29}, {"time": 0.5, "x": -7.98, "y": 7.61}, {"time": 0.6667, "x": -3.58, "y": 1.29}]}, "datui1": {"rotate": [{"angle": -6.15}, {"time": 0.1667, "angle": 17.56}, {"time": 0.3333, "angle": -6.15}, {"time": 0.5, "angle": 17.56}, {"time": 0.6667, "angle": -6.15}], "translate": [{}, {"time": 0.1667, "x": -1.77, "y": -0.39, "curve": "stepped"}, {"time": 0.3333, "x": -1.77, "y": -0.39, "curve": "stepped"}, {"time": 0.5, "x": -1.77, "y": -0.39, "curve": "stepped"}, {"time": 0.6667, "x": -1.77, "y": -0.39}], "scale": [{"x": 0.72}, {"time": 0.1667, "x": 1.02}, {"time": 0.3333, "x": 0.72}, {"time": 0.5, "x": 1.02}, {"time": 0.6667, "x": 0.72}], "shear": [{"x": -6.85}, {"time": 0.1667, "x": -4.56}, {"time": 0.3333, "x": -6.85}, {"time": 0.5, "x": -4.56}, {"time": 0.6667, "x": -6.85}]}, "datui": {"rotate": [{"angle": 7.08}, {"time": 0.1667, "angle": -11.93}, {"time": 0.3333, "angle": 7.08}, {"time": 0.5, "angle": -11.93}, {"time": 0.6667, "angle": 7.08}], "translate": [{"x": 4.43, "y": -1.07}, {"time": 0.1667, "x": -5.95, "y": 4.72}, {"time": 0.3333, "x": 4.43, "y": -1.07}, {"time": 0.5, "x": -5.95, "y": 4.72}, {"time": 0.6667, "x": 4.43, "y": -1.07}], "scale": [{"x": 1.1}, {"time": 0.1667, "x": 0.87}, {"time": 0.3333, "x": 1.1}, {"time": 0.5, "x": 0.87}, {"time": 0.6667, "x": 1.1}], "shear": [{"x": -1.43}, {"time": 0.1667, "x": -15}, {"time": 0.3333, "x": -1.43}, {"time": 0.5, "x": -15}, {"time": 0.6667, "x": -1.43}]}, "shenti": {"scale": [{}, {"time": 0.1667, "x": 1.02}, {"time": 0.3333}, {"time": 0.5, "x": 1.02}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}}}, "zhuamao": {"bones": {"datui1": {"rotate": [{"angle": -6.15}, {"time": 0.1667, "angle": 17.56}, {"time": 0.3333, "angle": -6.15}, {"time": 0.5, "angle": 17.56}, {"time": 0.6667, "angle": -6.15}], "translate": [{}, {"time": 0.1667, "x": -1.77, "y": -0.39, "curve": "stepped"}, {"time": 0.3333, "x": -1.77, "y": -0.39, "curve": "stepped"}, {"time": 0.5, "x": -1.77, "y": -0.39, "curve": "stepped"}, {"time": 0.6667, "x": -1.77, "y": -0.39}], "scale": [{"x": 0.72}, {"time": 0.1667, "x": 1.02}, {"time": 0.3333, "x": 0.72}, {"time": 0.5, "x": 1.02}, {"time": 0.6667, "x": 0.72}], "shear": [{"x": -6.85}, {"time": 0.1667, "x": -4.56}, {"time": 0.3333, "x": -6.85}, {"time": 0.5, "x": -4.56}, {"time": 0.6667, "x": -6.85}]}, "datui": {"rotate": [{"angle": 7.08}, {"time": 0.1667, "angle": -11.93}, {"time": 0.3333, "angle": 7.08}, {"time": 0.5, "angle": -11.93}, {"time": 0.6667, "angle": 7.08}], "translate": [{"x": 4.43, "y": -1.07}, {"time": 0.1667, "x": -5.95, "y": 4.72}, {"time": 0.3333, "x": 4.43, "y": -1.07}, {"time": 0.5, "x": -5.95, "y": 4.72}, {"time": 0.6667, "x": 4.43, "y": -1.07}], "scale": [{"x": 1.1}, {"time": 0.1667, "x": 0.87}, {"time": 0.3333, "x": 1.1}, {"time": 0.5, "x": 0.87}, {"time": 0.6667, "x": 1.1}], "shear": [{"x": -1.43}, {"time": 0.1667, "x": -15}, {"time": 0.3333, "x": -1.43}, {"time": 0.5, "x": -15}, {"time": 0.6667, "x": -1.43}]}, "shoubi": {"translate": [{"x": 91.41, "y": 41.76, "curve": "stepped"}, {"time": 0.6667, "x": 91.41, "y": 41.76}]}, "shenti": {"scale": [{}, {"time": 0.1667, "x": 1.02}, {"time": 0.3333}, {"time": 0.5, "x": 1.02}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}}}}}
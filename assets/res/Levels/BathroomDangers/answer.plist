<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>frames</key>
    <dict>
      <key>1.png</key>
      <dict>
        <key>frame</key>
        <string>{{1,1},{90,90}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{90,90}}</string>
        <key>sourceSize</key>
        <string>{90,90}</string>
      </dict>
      <key>2.png</key>
      <dict>
        <key>frame</key>
        <string>{{185,1},{90,90}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{90,90}}</string>
        <key>sourceSize</key>
        <string>{90,90}</string>
      </dict>
      <key>3.png</key>
      <dict>
        <key>frame</key>
        <string>{{277,1},{90,90}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{90,90}}</string>
        <key>sourceSize</key>
        <string>{90,90}</string>
      </dict>
      <key>4.png</key>
      <dict>
        <key>frame</key>
        <string>{{369,1},{90,90}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{90,90}}</string>
        <key>sourceSize</key>
        <string>{90,90}</string>
      </dict>
      <key>5.png</key>
      <dict>
        <key>frame</key>
        <string>{{461,1},{90,90}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{90,90}}</string>
        <key>sourceSize</key>
        <string>{90,90}</string>
      </dict>
      <key>6.png</key>
      <dict>
        <key>frame</key>
        <string>{{553,1},{90,90}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{90,90}}</string>
        <key>sourceSize</key>
        <string>{90,90}</string>
      </dict>
      <key>7.png</key>
      <dict>
        <key>frame</key>
        <string>{{645,1},{90,90}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{90,90}}</string>
        <key>sourceSize</key>
        <string>{90,90}</string>
      </dict>
      <key>8.png</key>
      <dict>
        <key>frame</key>
        <string>{{737,1},{90,90}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{90,90}}</string>
        <key>sourceSize</key>
        <string>{90,90}</string>
      </dict>
      <key>9.png</key>
      <dict>
        <key>frame</key>
        <string>{{829,1},{90,90}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{90,90}}</string>
        <key>sourceSize</key>
        <string>{90,90}</string>
      </dict>
      <key>10.png</key>
      <dict>
        <key>frame</key>
        <string>{{93,1},{90,90}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{90,90}}</string>
        <key>sourceSize</key>
        <string>{90,90}</string>
      </dict>
    </dict>
    <key>metadata</key>
    <dict>
      <key>format</key>
      <integer>2</integer>
      <key>realTextureFileName</key>
      <string>answer.png</string>
      <key>textureFileName</key>
      <string>answer.png</string>
      <key>size</key>
      <string>{0,0}</string>
    </dict>
  </dict>
</plist>